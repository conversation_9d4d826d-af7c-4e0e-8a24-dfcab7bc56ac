jQuery(document).ready(function($) {
    // Initialize the excluded posts list from hidden field
    function initExcludedPostsList() {
        const excludedPostsValue = $('#wp_manga_excluded_posts_ids').val();
        if (excludedPostsValue && excludedPostsValue.trim() !== '') {
            try {
                const postsArray = JSON.parse(excludedPostsValue);
                if (Array.isArray(postsArray)) {
                    postsArray.forEach(post => {
                        if (post && post.id && post.title) {
                            addPostToExclusionList(post.id, post.title);
                        }
                    });
                } else {
                    console.error('Excluded posts is not an array:', postsArray);
                }
            } catch (e) {
                console.error('Error parsing excluded posts:', e);
                console.log('Raw value:', excludedPostsValue);
            }
        }
    }

    // Add a post to the exclusion list
    function addPostToExclusionList(postId, postTitle) {
        // Check if already in list
        if ($('#excluded-posts-list li[data-post-id="' + postId + '"]').length > 0) {
            return;
        }
        
        // Create list item
        const listItem = $(`
            <li data-post-id="${postId}">
                <span class="post-title">${postTitle}</span> (ID: ${postId})
                <a href="#" class="remove-excluded-post">×</a>
            </li>
        `);
        
        // Append to list
        $('#excluded-posts-list').append(listItem);
        
        // Update hidden field
        updateExcludedPostsField();
    }

    // Update the hidden field with the current list of excluded posts
    function updateExcludedPostsField() {
        const posts = [];
        $('#excluded-posts-list li').each(function() {
            posts.push({
                id: $(this).data('post-id'),
                title: $(this).find('.post-title').text()
            });
        });
        
        const jsonString = JSON.stringify(posts);
        $('#wp_manga_excluded_posts_ids').val(jsonString);
        console.log('Updated excluded posts field with:', jsonString);
    }

    // Post search
    $('#post-search-input').on('keyup', function() {
        const searchTerm = $(this).val();
        if (searchTerm.length < 3) {
            $('#post-search-results').empty().hide();
            return;
        }

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'wp_manga_watermark_search_posts',
                search: searchTerm,
                nonce: wp_manga_watermark_vars.nonce
            },
            success: function(response) {
                const resultsList = $('#post-search-results');
                resultsList.empty();
                
                if (response.success && response.data.length > 0) {
                    response.data.forEach(post => {
                        const item = $(`<li data-post-id="${post.id}"><span class="post-title">${post.title}</span> (ID: ${post.id})</li>`);
                        resultsList.append(item);
                    });
                    resultsList.show();
                } else {
                    resultsList.hide();
                }
            }
        });
    });

    // Handle click on search result
    $(document).on('click', '#post-search-results li', function() {
        const postId = $(this).data('post-id');
        const postTitle = $(this).find('.post-title').text();
        
        // Check if already in list
        if ($('#excluded-posts-list li[data-post-id="' + postId + '"]').length === 0) {
            addPostToExclusionList(postId, postTitle);
        }
        
        // Clear search
        $('#post-search-input').val('');
        $('#post-search-results').empty().hide();
    });

    // Handle removing post from exclusion list
    $(document).on('click', '.remove-excluded-post', function(e) {
        e.preventDefault();
        $(this).closest('li').remove();
        updateExcludedPostsField();
    });

    // Initialize on page load
    initExcludedPostsList();
});
