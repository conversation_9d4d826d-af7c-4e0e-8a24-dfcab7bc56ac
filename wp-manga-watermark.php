<?php
	/**
	 *  Plugin Name: WP Manga - Watermark
	 *  Description: add watermark to uploaded chapter images
	 *  Plugin URI: https://www.mangabooth.com/
	 *  Author: <PERSON><PERSON><PERSON>ooth
	 *  Author URI: https://themeforest.net/user/wpstylish
	 *  Author Email: <EMAIL>
	 *  Version: 1.0.3
	 *  Text Domain: wp-manga-watermark
	 * @since 1.0
	 */

 // plugin dir URI
if ( ! defined( 'WP_MANGA_WATERMARK_URI' ) ) {
	define( 'WP_MANGA_WATERMARK_URI', plugin_dir_url( __FILE__ ) );
}

// plugin dir path
if ( ! defined( 'WP_MANGA_WATERMARK_DIR' ) ) {
	define( 'WP_MANGA_WATERMARK_DIR', plugin_dir_path( __FILE__ ) );
}

if ( ! defined( 'WP_MANGA_WATERMARK_TEXTDOMAIN' ) ) {
	define( 'WP_MANGA_WATERMARK_TEXTDOMAIN', 'wp-manga-watermark' );
}

class WP_MANGA_ADDON_WATERMARK {
	private static $instance;

	public static function get_instance() {
		if ( null == self::$instance ) {
			self::$instance = new WP_MANGA_ADDON_WATERMARK();
		}

		return self::$instance;
	}

	private function __construct() {
		add_action( 'init', array( $this, 'load_plugin_textdomain' ) );
		//add_action( 'admin_init', array($this, 'admin_init' ));
		//add_action( 'admin_menu', array($this, 'admin_menu' ));
		add_action( 'admin_enqueue_scripts', array( $this, 'admin_enqueue_script' ) );
	}
	
	function load_plugin_textdomain() {
		load_plugin_textdomain( 'wp-manga-watermark', false, plugin_basename( dirname( __FILE__ ) ) . '/languages' );
		
		add_action('wp_manga_upload_after_extract', array($this,'upload_after_extract'), 100, 4);
	}
	
	/**
	 * Add watermark to all images in Folder with frequency control
	 **/
	function add_watermark_folder($folder_path, $options, $watermark){
		$frequency = isset($options['frequency']) ? intval($options['frequency']) : 1;
		// ensure frequency is at least 1
		if($frequency < 1){
			$frequency = 1;
		}
		$file_counter = 0;
		foreach (glob($folder_path . '/*.*') as $file) {
			if(is_dir($file)){
				$this->add_watermark_folder($file, $options, $watermark);
			} else {
				$file_counter++;
				if($file_counter % $frequency == 0){
					$this->add_watermark_file($file, $options, $watermark);
				}
			}
		}
	}
	
	/**
	 * Add watermark to image file 
	 * $file - File path
	 * $options - array of options 
	 **/
	function add_watermark_file($file, $options, $watermark){
			// Get watermark extension
			$watermark_ext = strtolower(pathinfo($watermark, PATHINFO_EXTENSION));
			
			// For target file, use getimagesize to determine actual mime type
			$image_info = getimagesize($file);
			$mime_type = $image_info['mime'] ?? '';
			
			// Load watermark based on file type
			if($watermark_ext == 'png') {
				$stamp = imagecreatefrompng($watermark);
			} elseif($watermark_ext == 'webp') {
				$stamp = imagecreatefromwebp($watermark);
			} else {
				$stamp = imagecreatefromjpeg($watermark);
			}
			
			// Load target image based on actual mime type
			if($mime_type == 'image/png') {
				$im = imagecreatefrompng($file);
				$file_type = 'png';
			} elseif($mime_type == 'image/webp') {
				$im = imagecreatefromwebp($file);
				$file_type = 'webp';
			} else {
				$im = imagecreatefromjpeg($file);
				$file_type = 'jpeg';
			}
			
			$position = isset($options['position']) ? $options['position'] : '0,0';
			
			$position_base = isset($options['position_base']) ? $options['position_base'] : 'bottom-right';
			list($marge_right, $marge_bottom) = explode(',', $position);
			
			$sx = imagesx($stamp);
			$sy = imagesy($stamp);
	
			// Copy the stamp image onto our photo using the margin offsets and the photo 
			// width to calculate positioning of the stamp.
			
			$des_width = imagesx($im);
			$des_height = imagesy($im);
			
			$xpos = $des_width - $sx - $marge_right;
			$ypos = $des_height - $sy - $marge_bottom;
			
			switch($position_base){
				case 'bottom-right':
					break;
				case 'bottom-left':
					$xpos = $marge_right;
					$ypos = $des_height - $sy - $marge_bottom;
					break;
				case 'top-right':
					$xpos = $des_width - $sx - $marge_right;
					$ypos = $marge_bottom;
					break;
				case 'top-left':
					$xpos = $marge_right;
					$ypos = $marge_bottom;
					break;
			}
			
			//create a fully transparent background (127 means fully transparent)
			$trans_background = imagecolorallocatealpha($im, 0, 0, 0, 127);
			
			$dest_image = imagecreatetruecolor($des_width, $des_height);
			//make sure the transparency information is saved
			imagesavealpha($dest_image, true);
	
			//create a fully transparent background (127 means fully transparent)
			$trans_background = imagecolorallocatealpha($dest_image, 0, 0, 0, 127);
	
			//fill the image with a transparent background
			imagefill($dest_image, 0, 0, $trans_background);
			
			imagecopy($dest_image, $im, 0, 0, 0, 0, $des_width, $des_height);
			imagecopy($dest_image, $stamp, $xpos, $ypos, 0, 0, $sx, $sy);
	
			// resave based on detected file type rather than extension
			if($file_type == 'png') {
				imagepng($dest_image, $file);
			} elseif($file_type == 'webp') {
				imagewebp($dest_image, $file, 90); // Quality 90 for WebP (range: 0-100)
			} else {
				imagejpeg($dest_image, $file, 99);
			}
			
			// Free up memory
			imagedestroy($stamp);	
			imagedestroy($im);
			imagedestroy($dest_image);	
	}
	
	// after upload, scan all folder images to add watermark
	function upload_after_extract($post_id, $slugified_name, $extract, $storage){
		$options = get_option('wp_manga_watermark_options' , array());
		
		// Check for excluded posts option with better JSON handling
		if(!empty($options['excluded_posts'])){
			try {
				$excluded_posts = json_decode($options['excluded_posts'], true);
				
				if(is_array($excluded_posts)){
					foreach($excluded_posts as $post){
						if(isset($post['id']) && ($post['id'] == $post_id || $post['id'] == (string)$post_id)){
							return; // Skip this post
						}
					}
				}
			} catch (Exception $e) {
				// Log error or handle it
				error_log('Error parsing excluded posts JSON: ' . $e->getMessage());
			}
		}
		
		// If chapter has over 60 images, override frequency (if needed)
		$files = glob($extract . '/*.*');
		if($files && count($files) > 60){
			$options['frequency'] = 5;
		}
		
		if(isset($options['attachment_id']) && $options['attachment_id'] != 0){
			$watermark = wp_get_attachment_url($options['attachment_id']);
			if($watermark){
				$this->add_watermark_folder($extract, $options, str_replace(home_url('/'), ABSPATH, $watermark));
			}
		}
	}
	
	function admin_enqueue_script(){
		wp_enqueue_script( 'wp-manga-watermark-admin', WP_MANGA_WATERMARK_URI . 'assets/js/wp-watermark.js', array('jquery'), '', true );
		wp_enqueue_style( 'wp-manga-watermark-admin', WP_MANGA_WATERMARK_URI . 'assets/css/admin.css');
		
		// Only load on our settings page
		$screen = get_current_screen();
		if(isset($screen->id) && $screen->id == 'wp-manga_page_wp-manga-watermark-settings') {
			wp_enqueue_script('wp-manga-watermark-excluded-posts', WP_MANGA_WATERMARK_URI . 'assets/js/excluded-posts.js', array('jquery'), '', true);
			wp_localize_script('wp-manga-watermark-excluded-posts', 'wp_manga_watermark_vars', array(
				'nonce' => wp_create_nonce('wp_manga_watermark_nonce')
			));
			
			// Add custom CSS for the excluded posts UI
			wp_add_inline_style('wp-manga-watermark-admin', '
				.excluded-posts-container { margin-top: 10px; }
				.excluded-posts-search { margin-bottom: 10px; position: relative; }
				#post-search-results { 
					list-style: none; 
					border: 1px solid #ddd; 
					max-height: 200px; 
					overflow-y: auto; 
					position: absolute; 
					background: white; 
					width: 100%; 
					z-index: 100;
					margin: 0;
					padding: 0;
					display: none;
				}
				#post-search-results li { 
					padding: 8px 12px; 
					cursor: pointer; 
					border-bottom: 1px solid #f0f0f0;
				}
				#post-search-results li:hover { background: #f0f0f0; }
				#excluded-posts-list { 
					list-style: none; 
					margin: 0;
					padding: 0;
				}
				#excluded-posts-list li { 
					padding: 8px 12px; 
					background: #f9f9f9; 
					border: 1px solid #ddd;
					margin-bottom: 5px;
				}
				.remove-excluded-post {
					float: right;
					color: #a00;
					text-decoration: none;
					font-weight: bold;
				}
				.remove-excluded-post:hover { color: red; }
			');
		}
	}
}

$wp_manga_watermark = WP_MANGA_ADDON_WATERMARK::get_instance();
require_once('admin/settings-page.php');

// Add AJAX handler for post search
add_action('wp_ajax_wp_manga_watermark_search_posts', 'wp_manga_watermark_search_posts_callback');
function wp_manga_watermark_search_posts_callback() {
	check_ajax_referer('wp_manga_watermark_nonce', 'nonce');
	
	$search = sanitize_text_field($_POST['search']);
	
	$args = array(
		'post_type' => 'wp-manga',
		'post_status' => 'publish',
		's' => $search,
		'posts_per_page' => 10,
	);
	
	$query = new WP_Query($args);
	$results = array();
	
	if($query->have_posts()) {
		while($query->have_posts()) {
			$query->the_post();
			$results[] = array(
				'id' => get_the_ID(),
				'title' => get_the_title()
			);
		}
	}
	
	wp_reset_postdata();
	
	wp_send_json_success($results);
}